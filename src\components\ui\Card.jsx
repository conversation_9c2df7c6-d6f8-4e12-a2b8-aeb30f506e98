/**
 * Card Component
 * Reusable card component for content containers
 */

export default function Card({
  children,
  className = '',
  padding = 'md',
  shadow = 'soft',
  hover = false,
  ...props
}) {
  const baseClasses = 'bg-white rounded-xl border border-neutral-200 transition-all duration-200';
  
  const paddingClasses = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
    xl: 'p-10',
  };
  
  const shadowClasses = {
    none: '',
    soft: 'shadow-soft',
    medium: 'shadow-medium',
    large: 'shadow-large',
  };
  
  const hoverClasses = hover ? 'hover:shadow-medium hover:-translate-y-1' : '';
  
  const classes = `${baseClasses} ${paddingClasses[padding]} ${shadowClasses[shadow]} ${hoverClasses} ${className}`;
  
  return (
    <div className={classes} {...props}>
      {children}
    </div>
  );
}

// Card sub-components
Card.Header = function CardHeader({ children, className = '', ...props }) {
  return (
    <div className={`border-b border-neutral-200 pb-4 mb-4 ${className}`} {...props}>
      {children}
    </div>
  );
};

Card.Body = function CardBody({ children, className = '', ...props }) {
  return (
    <div className={className} {...props}>
      {children}
    </div>
  );
};

Card.Footer = function CardFooter({ children, className = '', ...props }) {
  return (
    <div className={`border-t border-neutral-200 pt-4 mt-4 ${className}`} {...props}>
      {children}
    </div>
  );
};
