/**
 * Validation Utilities
 * <PERSON><PERSON><PERSON> hàm tiện ích để validate form data
 */

// Validate email format
export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Validate password strength
export const validatePassword = (password) => {
  const errors = [];
  
  if (password.length < 6) {
    errors.push('Mật khẩu phải có ít nhất 6 ký tự');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Mật khẩu phải có ít nhất 1 chữ hoa');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Mật khẩu phải có ít nhất 1 chữ thường');
  }
  
  if (!/[0-9]/.test(password)) {
    errors.push('<PERSON><PERSON>t khẩu phải có ít nhất 1 số');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Validate phone number (Vietnamese format)
export const validatePhone = (phone) => {
  const phoneRegex = /^(0[3|5|7|8|9])+([0-9]{8})$/;
  return phoneRegex.test(phone);
};

// Validate student ID
export const validateStudentId = (studentId) => {
  // Student ID should be alphanumeric and 6-10 characters
  const studentIdRegex = /^[A-Za-z0-9]{6,10}$/;
  return studentIdRegex.test(studentId);
};

// Validate full name
export const validateFullName = (name) => {
  // Name should contain only letters, spaces, and Vietnamese characters
  const nameRegex = /^[a-zA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạảấầẩẫậắằẳẵặẹẻẽềềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửữựỳỵýỷỹ\s]+$/;
  return nameRegex.test(name) && name.trim().length >= 2;
};

// General form validation
export const validateForm = (data, rules) => {
  const errors = {};
  
  Object.keys(rules).forEach(field => {
    const value = data[field];
    const fieldRules = rules[field];
    
    // Required validation
    if (fieldRules.required && (!value || value.toString().trim() === '')) {
      errors[field] = fieldRules.requiredMessage || `${field} là bắt buộc`;
      return;
    }
    
    // Skip other validations if field is empty and not required
    if (!value || value.toString().trim() === '') {
      return;
    }
    
    // Email validation
    if (fieldRules.email && !validateEmail(value)) {
      errors[field] = fieldRules.emailMessage || 'Email không hợp lệ';
      return;
    }
    
    // Phone validation
    if (fieldRules.phone && !validatePhone(value)) {
      errors[field] = fieldRules.phoneMessage || 'Số điện thoại không hợp lệ';
      return;
    }
    
    // Student ID validation
    if (fieldRules.studentId && !validateStudentId(value)) {
      errors[field] = fieldRules.studentIdMessage || 'Mã số học sinh không hợp lệ';
      return;
    }
    
    // Full name validation
    if (fieldRules.fullName && !validateFullName(value)) {
      errors[field] = fieldRules.fullNameMessage || 'Họ và tên không hợp lệ';
      return;
    }
    
    // Min length validation
    if (fieldRules.minLength && value.length < fieldRules.minLength) {
      errors[field] = fieldRules.minLengthMessage || `${field} phải có ít nhất ${fieldRules.minLength} ký tự`;
      return;
    }
    
    // Max length validation
    if (fieldRules.maxLength && value.length > fieldRules.maxLength) {
      errors[field] = fieldRules.maxLengthMessage || `${field} không được vượt quá ${fieldRules.maxLength} ký tự`;
      return;
    }
    
    // Custom validation
    if (fieldRules.custom && typeof fieldRules.custom === 'function') {
      const customResult = fieldRules.custom(value, data);
      if (customResult !== true) {
        errors[field] = customResult;
        return;
      }
    }
  });
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

// Password confirmation validation
export const validatePasswordConfirmation = (password, confirmPassword) => {
  if (password !== confirmPassword) {
    return 'Mật khẩu xác nhận không khớp';
  }
  return true;
};
