import "./globals.css";

export const metadata = {
  title: "<PERSON><PERSON> thống Quản lý <PERSON> sinh",
  description: "<PERSON><PERSON> thống quản lý học sinh hiện đại với 3 role: <PERSON><PERSON>, Teacher, Student",
  keywords: "qu<PERSON><PERSON> lý họ<PERSON> sinh, g<PERSON><PERSON><PERSON>, t<PERSON><PERSON><PERSON><PERSON>, admin, g<PERSON><PERSON><PERSON> vi<PERSON>, h<PERSON><PERSON> sin<PERSON>",
  authors: [{ name: "Student Management System" }],
  viewport: "width=device-width, initial-scale=1",
};

export default function RootLayout({ children }) {
  return (
    <html lang="vi" className="scroll-smooth">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body className="font-sans antialiased bg-neutral-50 text-neutral-900 min-h-screen">
        <div id="root" className="min-h-screen">
          {children}
        </div>
      </body>
    </html>
  );
}
