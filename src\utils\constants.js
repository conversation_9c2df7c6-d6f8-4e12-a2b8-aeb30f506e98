/**
 * Application Constants
 * <PERSON><PERSON>c hằng số được sử dụng trong toàn bộ ứng dụng
 */

// User Roles
export const USER_ROLES = {
  ADMIN: 'admin',
  TEACHER: 'teacher',
  STUDENT: 'student'
};

// Role Display Names
export const ROLE_NAMES = {
  [USER_ROLES.ADMIN]: 'Quản trị viên',
  [USER_ROLES.TEACHER]: 'Giáo viên',
  [USER_ROLES.STUDENT]: 'Học sinh'
};

// API Endpoints (for future use)
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    LOGOUT: '/api/auth/logout',
    REFRESH: '/api/auth/refresh',
    FORGOT_PASSWORD: '/api/auth/forgot-password',
    RESET_PASSWORD: '/api/auth/reset-password'
  },
  USERS: {
    GET_PROFILE: '/api/users/profile',
    UPDATE_PROFILE: '/api/users/profile',
    GET_USERS: '/api/users',
    CREATE_USER: '/api/users',
    UPDATE_USER: '/api/users',
    DELETE_USER: '/api/users'
  },
  STUDENTS: {
    GET_STUDENTS: '/api/students',
    GET_STUDENT: '/api/students',
    CREATE_STUDENT: '/api/students',
    UPDATE_STUDENT: '/api/students',
    DELETE_STUDENT: '/api/students'
  },
  TEACHERS: {
    GET_TEACHERS: '/api/teachers',
    GET_TEACHER: '/api/teachers',
    CREATE_TEACHER: '/api/teachers',
    UPDATE_TEACHER: '/api/teachers',
    DELETE_TEACHER: '/api/teachers'
  },
  CLASSES: {
    GET_CLASSES: '/api/classes',
    GET_CLASS: '/api/classes',
    CREATE_CLASS: '/api/classes',
    UPDATE_CLASS: '/api/classes',
    DELETE_CLASS: '/api/classes'
  }
};

// Local Storage Keys
export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_DATA: 'user_data',
  THEME: 'theme',
  LANGUAGE: 'language'
};

// Form Validation Rules
export const VALIDATION_RULES = {
  EMAIL: {
    required: true,
    email: true,
    requiredMessage: 'Email là bắt buộc',
    emailMessage: 'Email không hợp lệ'
  },
  PASSWORD: {
    required: true,
    minLength: 6,
    requiredMessage: 'Mật khẩu là bắt buộc',
    minLengthMessage: 'Mật khẩu phải có ít nhất 6 ký tự'
  },
  FULL_NAME: {
    required: true,
    fullName: true,
    minLength: 2,
    maxLength: 50,
    requiredMessage: 'Họ và tên là bắt buộc',
    fullNameMessage: 'Họ và tên không hợp lệ',
    minLengthMessage: 'Họ và tên phải có ít nhất 2 ký tự',
    maxLengthMessage: 'Họ và tên không được vượt quá 50 ký tự'
  },
  PHONE: {
    phone: true,
    phoneMessage: 'Số điện thoại không hợp lệ (10-11 số)'
  },
  STUDENT_ID: {
    required: true,
    studentId: true,
    requiredMessage: 'Mã số học sinh là bắt buộc',
    studentIdMessage: 'Mã số học sinh không hợp lệ (6-10 ký tự alphanumeric)'
  }
};

// Demo Accounts
export const DEMO_ACCOUNTS = {
  ADMIN: {
    email: '<EMAIL>',
    password: 'admin123',
    role: USER_ROLES.ADMIN,
    fullName: 'Nguyễn Văn Admin'
  },
  TEACHER: {
    email: '<EMAIL>',
    password: 'teacher123',
    role: USER_ROLES.TEACHER,
    fullName: 'Trần Thị Giáo Viên'
  },
  STUDENT: {
    email: '<EMAIL>',
    password: 'student123',
    role: USER_ROLES.STUDENT,
    fullName: 'Lê Văn Học Sinh',
    studentId: 'SV001'
  }
};

// Application Settings
export const APP_CONFIG = {
  APP_NAME: 'Hệ thống Quản lý Học sinh',
  APP_VERSION: '1.0.0',
  APP_DESCRIPTION: 'Hệ thống quản lý học sinh hiện đại với 3 role: Admin, Teacher, Student',
  COMPANY_NAME: 'School Management System',
  SUPPORT_EMAIL: '<EMAIL>',
  WEBSITE_URL: 'https://school.edu'
};

// Pagination Settings
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: [5, 10, 20, 50],
  MAX_PAGE_SIZE: 100
};

// Date Formats
export const DATE_FORMATS = {
  DISPLAY: 'DD/MM/YYYY',
  DISPLAY_WITH_TIME: 'DD/MM/YYYY HH:mm',
  API: 'YYYY-MM-DD',
  API_WITH_TIME: 'YYYY-MM-DD HH:mm:ss'
};

// Status Constants
export const STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  PENDING: 'pending',
  SUSPENDED: 'suspended'
};

// Status Display Names
export const STATUS_NAMES = {
  [STATUS.ACTIVE]: 'Hoạt động',
  [STATUS.INACTIVE]: 'Không hoạt động',
  [STATUS.PENDING]: 'Chờ duyệt',
  [STATUS.SUSPENDED]: 'Tạm ngưng'
};

// Grade Levels
export const GRADE_LEVELS = {
  GRADE_1: '1',
  GRADE_2: '2',
  GRADE_3: '3',
  GRADE_4: '4',
  GRADE_5: '5',
  GRADE_6: '6',
  GRADE_7: '7',
  GRADE_8: '8',
  GRADE_9: '9',
  GRADE_10: '10',
  GRADE_11: '11',
  GRADE_12: '12'
};

// Subject Constants
export const SUBJECTS = {
  MATH: 'math',
  LITERATURE: 'literature',
  ENGLISH: 'english',
  PHYSICS: 'physics',
  CHEMISTRY: 'chemistry',
  BIOLOGY: 'biology',
  HISTORY: 'history',
  GEOGRAPHY: 'geography',
  CIVIC_EDUCATION: 'civic_education',
  PHYSICAL_EDUCATION: 'physical_education',
  TECHNOLOGY: 'technology',
  INFORMATICS: 'informatics'
};

// Subject Display Names
export const SUBJECT_NAMES = {
  [SUBJECTS.MATH]: 'Toán học',
  [SUBJECTS.LITERATURE]: 'Ngữ văn',
  [SUBJECTS.ENGLISH]: 'Tiếng Anh',
  [SUBJECTS.PHYSICS]: 'Vật lý',
  [SUBJECTS.CHEMISTRY]: 'Hóa học',
  [SUBJECTS.BIOLOGY]: 'Sinh học',
  [SUBJECTS.HISTORY]: 'Lịch sử',
  [SUBJECTS.GEOGRAPHY]: 'Địa lý',
  [SUBJECTS.CIVIC_EDUCATION]: 'Giáo dục công dân',
  [SUBJECTS.PHYSICAL_EDUCATION]: 'Thể dục',
  [SUBJECTS.TECHNOLOGY]: 'Công nghệ',
  [SUBJECTS.INFORMATICS]: 'Tin học'
};
