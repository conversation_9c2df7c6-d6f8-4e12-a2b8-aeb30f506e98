# 🎓 Hệ thống Quản lý Học sinh

Một hệ thống quản lý học sinh hiện đại được xây dựng với **Next.js** và **TailwindCSS**, hỗ trợ 3 vai trò chính: **Admin**, **Giáo viên** và **Học sinh**.

## ✨ Tính năng chính

### 🔐 Hệ thống xác thực
- Đăng nhập/Đăng ký với validation đầy đủ
- Phân quyền theo 3 vai trò: <PERSON><PERSON>, Teacher, Student
- Form validation client-side với HTML5 và JavaScript

### 🎨 Giao diện người dùng
- Thiết kế responsive, tương thích mọi thiết bị
- Color palette nhẹ nhàng, phù hợp môi trường giáo dục
- Component UI tái sử dụng (Button, Input, Card)
- Animation mượt mà với Tailwind CSS

### 📱 Responsive Design
- Mobile-first approach
- Tối ưu cho tablet và desktop
- Touch-friendly interface

## 🚀 Cài đặt và Chạy dự án

### Yêu cầu hệ thống
- Node.js 18.0 hoặc cao hơn
- npm, yarn, pnpm hoặc bun

### Bước 1: Clone repository
```bash
git clone <repository-url>
cd manage-students-fe
```

### Bước 2: Cài đặt dependencies
```bash
npm install
# hoặc
yarn install
# hoặc
pnpm install
```

### Bước 3: Chạy development server
```bash
npm run dev
# hoặc
yarn dev
# hoặc
pnpm dev
```

### Bước 4: Mở trình duyệt
Truy cập [http://localhost:3000](http://localhost:3000) để xem ứng dụng.

## 📁 Cấu trúc thư mục

```
src/
├── app/                    # App Router (Next.js 13+)
│   ├── auth/              # Trang xác thực
│   │   ├── login/         # Trang đăng nhập
│   │   └── register/      # Trang đăng ký
│   ├── admin/             # Dashboard Admin
│   ├── teacher/           # Dashboard Giáo viên
│   ├── student/           # Dashboard Học sinh
│   ├── globals.css        # CSS toàn cục
│   ├── layout.js          # Layout chính
│   └── page.js            # Trang chủ
├── components/            # React Components
│   ├── ui/                # UI Components cơ bản
│   │   ├── Button.jsx     # Component Button
│   │   ├── Input.jsx      # Component Input
│   │   └── Card.jsx       # Component Card
│   ├── layout/            # Layout Components
│   └── forms/             # Form Components
├── lib/                   # Utilities và helpers
├── utils/                 # Utility functions
├── hooks/                 # Custom React hooks
└── contexts/              # React Context providers
```

## 🎨 Thiết kế và Styling

### Color Palette
- **Primary**: Xanh dương nhẹ (#3b82f6) - Chủ đạo
- **Secondary**: Xanh lá nhẹ (#22c55e) - Thành công
- **Accent**: Cam ấm (#f97316) - Nhấn mạnh
- **Neutral**: Xám mềm (#64748b) - Văn bản

### Typography
- **Font chính**: Inter - Dễ đọc, hiện đại
- **Font tiêu đề**: Poppins - Nổi bật, chuyên nghiệp

### Components
- **Button**: 6 variants (primary, secondary, accent, outline, ghost, danger)
- **Input**: Validation states, error handling
- **Card**: Flexible container với shadow effects

## 🔑 Tài khoản Demo

Sử dụng các tài khoản sau để test hệ thống:

| Vai trò | Email | Mật khẩu |
|---------|-------|----------|
| Admin | <EMAIL> | admin123 |
| Giáo viên | <EMAIL> | teacher123 |
| Học sinh | <EMAIL> | student123 |

## 🛠️ Công nghệ sử dụng

- **Framework**: Next.js 14 (App Router)
- **Styling**: TailwindCSS
- **Language**: JavaScript (ES6+)
- **Icons**: Heroicons (SVG)
- **Fonts**: Google Fonts (Inter, Poppins)

## 📝 Scripts có sẵn

```bash
npm run dev          # Chạy development server
npm run build        # Build production
npm run start        # Chạy production server
npm run lint         # Kiểm tra ESLint
```

## 🔧 Cấu hình

### Tailwind CSS
File `tailwind.config.js` đã được cấu hình với:
- Custom color palette
- Font families
- Spacing utilities
- Border radius
- Box shadows

### Next.js
- App Router được kích hoạt
- Tối ưu hóa font tự động
- Image optimization
- CSS Modules support

## 🚀 Deployment

### Vercel (Khuyến nghị)
1. Push code lên GitHub
2. Kết nối repository với Vercel
3. Deploy tự động

### Netlify
1. Build project: `npm run build`
2. Upload thư mục `out/` lên Netlify

### Docker
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

## 🤝 Đóng góp

1. Fork repository
2. Tạo feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Tạo Pull Request

## 📄 License

Dự án này được phát hành dưới [MIT License](LICENSE).

## 📞 Liên hệ

- **Email**: <EMAIL>
- **Website**: [https://school.edu](https://school.edu)
- **GitHub**: [Repository Link](https://github.com/username/manage-students-fe)

---

**Được phát triển với ❤️ bởi Team Development**
